<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sevb.mapper.UserMapper">
    <select id="getUserList" resultType="user">
        select * from t_user
    </select>
    <select id="getUserById" resultType="com.sevb.entry.User">
        select * from t_user where id = #{id}
    </select>

    <insert id="insertUser" parameterType="user">
        insert into t_user (username, address, gender) values (#{username}, #{address}, #{gender})
    </insert>

    <delete id="deleteUser" parameterType="int">
        delete from t_user where id = #{id}
    </delete>

    <update id="updateUser" parameterType="user">
        update t_user set username = #{username}, address = #{address}, gender = #{gender} where id = #{id}
    </update>
</mapper>