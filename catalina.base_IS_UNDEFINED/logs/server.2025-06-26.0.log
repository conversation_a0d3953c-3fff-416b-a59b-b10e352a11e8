2025-06-26 10:40:23.877 [main] INFO  com.sevb.BootDemoApplication - Starting BootDemoApplication using Java 17.0.4.1 with PID 29292 (D:\workspace\mashibing\project\springboot3\boot-demo-1\target\classes started by zhangzheng in D:\workspace\mashibing\project\springboot3)
2025-06-26 10:40:23.880 [main] INFO  com.sevb.BootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 10:40:25.115 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tom<PERSON> initialized with port 8080 (http)
2025-06-26 10:40:25.129 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-26 10:40:25.131 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 10:40:25.131 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 10:40:25.187 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/sevb] - Initializing Spring embedded WebApplicationContext
2025-06-26 10:40:25.187 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1266 ms
2025-06-26 10:40:25.270 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-26 10:40:26.338 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 10:40:27.307 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-26 10:40:27.333 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/sevb'
2025-06-26 10:40:27.346 [main] INFO  com.sevb.BootDemoApplication - Started BootDemoApplication in 3.973 seconds (process running for 4.819)
2025-06-26 11:20:03.632 [http-nio-8080-exec-4] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/sevb] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 11:20:03.639 [http-nio-8080-exec-4] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 11:20:03.650 [http-nio-8080-exec-4] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 10 ms
2025-06-26 11:20:30.618 [http-nio-8080-exec-5] DEBUG com.sevb.mapper.UserMapper.getUserList - ==>  Preparing: select * from t_user
2025-06-26 11:20:30.979 [http-nio-8080-exec-5] DEBUG com.sevb.mapper.UserMapper.getUserList - ==> Parameters: 
2025-06-26 11:20:31.027 [http-nio-8080-exec-5] TRACE com.sevb.mapper.UserMapper.getUserList - <==    Columns: id, username, address, gender
2025-06-26 11:20:31.027 [http-nio-8080-exec-5] TRACE com.sevb.mapper.UserMapper.getUserList - <==        Row: 4, 张三, 北京, 男
2025-06-26 11:20:31.038 [http-nio-8080-exec-5] TRACE com.sevb.mapper.UserMapper.getUserList - <==        Row: 5, 李四, 上海, 男
2025-06-26 11:20:31.039 [http-nio-8080-exec-5] TRACE com.sevb.mapper.UserMapper.getUserList - <==        Row: 6, 王五, 深圳, 女
2025-06-26 11:20:31.039 [http-nio-8080-exec-5] DEBUG com.sevb.mapper.UserMapper.getUserList - <==      Total: 3
2025-06-26 11:21:09.096 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 11:21:09.355 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 11:21:09.377 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-26 11:21:09.381 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
