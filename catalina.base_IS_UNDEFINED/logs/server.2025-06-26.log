2025-06-26 09:46:43.660 [main] INFO  com.sevb.BootDemoApplication - Starting BootDemoApplication using Java 17.0.4.1 with PID 13832 (D:\workspace\mashibing\project\springboot3\boot-demo-1\target\classes started by zhangzheng in D:\workspace\mashibing\project\springboot3)
2025-06-26 09:46:43.664 [main] INFO  com.sevb.BootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:46:45.031 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tom<PERSON> initialized with port 8080 (http)
2025-06-26 09:46:45.044 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-26 09:46:45.047 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 09:46:45.047 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 09:46:45.104 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/sevb] - Initializing Spring embedded WebApplicationContext
2025-06-26 09:46:45.104 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1387 ms
2025-06-26 09:46:45.208 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-26 09:46:46.439 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 09:46:47.028 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-26 09:46:47.049 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/sevb'
2025-06-26 09:46:47.060 [main] INFO  com.sevb.BootDemoApplication - Started BootDemoApplication in 4.07 seconds (process running for 4.934)
2025-06-26 09:51:38.082 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/sevb] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 09:51:38.084 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 09:51:38.088 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-26 09:52:08.109 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 09:52:08.326 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 09:52:08.340 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-26 09:52:08.351 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-26 09:52:11.794 [main] INFO  com.sevb.BootDemoApplication - Starting BootDemoApplication using Java 17.0.4.1 with PID 19960 (D:\workspace\mashibing\project\springboot3\boot-demo-1\target\classes started by zhangzheng in D:\workspace\mashibing\project\springboot3)
2025-06-26 09:52:11.796 [main] INFO  com.sevb.BootDemoApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 09:52:13.102 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-26 09:52:13.119 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-26 09:52:13.121 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 09:52:13.121 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-26 09:52:13.178 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/sevb] - Initializing Spring embedded WebApplicationContext
2025-06-26 09:52:13.179 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1337 ms
2025-06-26 09:52:13.272 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-26 09:52:14.460 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 09:52:15.131 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-26 09:52:15.153 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/sevb'
2025-06-26 09:52:15.165 [main] INFO  com.sevb.BootDemoApplication - Started BootDemoApplication in 3.955 seconds (process running for 4.979)
2025-06-26 09:52:34.941 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/sevb] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 09:52:34.941 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 09:52:34.944 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-26 09:52:35.014 [http-nio-8080-exec-1] DEBUG com.sevb.mapper.UserMapper.getUserList - ==>  Preparing: select * from t_user
2025-06-26 09:52:35.258 [http-nio-8080-exec-1] DEBUG com.sevb.mapper.UserMapper.getUserList - ==> Parameters: 
2025-06-26 09:52:35.299 [http-nio-8080-exec-1] TRACE com.sevb.mapper.UserMapper.getUserList - <==    Columns: id, username, address, gender
2025-06-26 09:52:35.300 [http-nio-8080-exec-1] TRACE com.sevb.mapper.UserMapper.getUserList - <==        Row: 4, 张三, 北京, 男
2025-06-26 09:52:35.306 [http-nio-8080-exec-1] TRACE com.sevb.mapper.UserMapper.getUserList - <==        Row: 5, 李四, 上海, 男
2025-06-26 09:52:35.306 [http-nio-8080-exec-1] TRACE com.sevb.mapper.UserMapper.getUserList - <==        Row: 6, 王五, 深圳, 女
2025-06-26 09:52:35.307 [http-nio-8080-exec-1] DEBUG com.sevb.mapper.UserMapper.getUserList - <==      Total: 3
2025-06-26 10:36:35.140 [SpringApplicationShutdownHook] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-26 10:36:35.426 [tomcat-shutdown] INFO  o.s.boot.web.embedded.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-26 10:36:35.435 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-26 10:36:35.465 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
